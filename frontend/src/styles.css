/* Global Styles with Modern Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css');
@import './app/shared/animations.css';

/* CSS Custom Properties for Design System */
:root {
  /* Primary Colors */
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-200: #90caf9;
  --primary-300: #64b5f6;
  --primary-400: #42a5f5;
  --primary-500: #2196f3;
  --primary-600: #1e88e5;
  --primary-700: #1976d2;
  --primary-800: #1565c0;
  --primary-900: #0d47a1;

  /* Secondary Colors */
  --secondary-50: #f3e5f5;
  --secondary-100: #e1bee7;
  --secondary-200: #ce93d8;
  --secondary-300: #ba68c8;
  --secondary-400: #ab47bc;
  --secondary-500: #9c27b0;
  --secondary-600: #8e24aa;
  --secondary-700: #7b1fa2;
  --secondary-800: #6a1b9a;
  --secondary-900: #4a148c;

  /* Success Colors */
  --success-50: #e8f5e8;
  --success-100: #c8e6c9;
  --success-200: #a5d6a7;
  --success-300: #81c784;
  --success-400: #66bb6a;
  --success-500: #4caf50;
  --success-600: #43a047;
  --success-700: #388e3c;
  --success-800: #2e7d32;
  --success-900: #1b5e20;

  /* Warning Colors */
  --warning-50: #fff8e1;
  --warning-100: #ffecb3;
  --warning-200: #ffe082;
  --warning-300: #ffd54f;
  --warning-400: #ffca28;
  --warning-500: #ffc107;
  --warning-600: #ffb300;
  --warning-700: #ffa000;
  --warning-800: #ff8f00;
  --warning-900: #ff6f00;

  /* Error Colors */
  --error-50: #ffebee;
  --error-100: #ffcdd2;
  --error-200: #ef9a9a;
  --error-300: #e57373;
  --error-400: #ef5350;
  --error-500: #f44336;
  --error-600: #e53935;
  --error-700: #d32f2f;
  --error-800: #c62828;
  --error-900: #b71c1c;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-600) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-400) 0%, var(--success-600) 100%);
  --gradient-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(33, 150, 243, 0.3);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-color: #475569;
}

/* Light Theme Variables */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --border-color: #e2e8f0;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Roboto', "Helvetica Neue", sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* Utility Classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

/* Custom Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Material Design Customizations */
.mat-mdc-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal) !important;
}

.mat-mdc-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl) !important;
}

.mat-mdc-button {
  border-radius: var(--radius-lg) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all var(--transition-fast) !important;
}

.mat-mdc-raised-button {
  box-shadow: var(--shadow-md) !important;
}

.mat-mdc-raised-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg) !important;
}

.mat-mdc-form-field {
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.mat-mdc-form-field .mat-mdc-text-field-wrapper {
  border-radius: var(--radius-lg) !important;
}

.mat-mdc-form-field.mat-form-field-appearance-outline .mat-mdc-form-field-outline {
  border-radius: var(--radius-lg) !important;
}

.mat-mdc-snack-bar-container {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-xl) !important;
}

.mat-mdc-progress-spinner {
  --mdc-circular-progress-active-indicator-color: var(--primary-500);
}

/* Custom Button Variants */
.btn-gradient-primary {
  background: var(--gradient-primary) !important;
  color: white !important;
  border: none !important;
  box-shadow: var(--shadow-md) !important;
}

.btn-gradient-primary:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px);
}

.btn-gradient-secondary {
  background: var(--gradient-secondary) !important;
  color: white !important;
  border: none !important;
  box-shadow: var(--shadow-md) !important;
}

.btn-gradient-secondary:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px);
}

.btn-glass {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: var(--text-primary) !important;
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Glassmorphism Effects */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
  .mat-mdc-card {
    margin: var(--spacing-sm);
    border-radius: var(--radius-lg) !important;
  }

  .mat-mdc-form-field {
    margin-bottom: var(--spacing-sm);
  }

  .mat-mdc-button {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  body {
    font-size: 14px;
  }

  .mat-mdc-card {
    margin: var(--spacing-xs);
    padding: var(--spacing-md);
  }
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .mat-mdc-card {
    border: 2px solid var(--neutral-800);
  }

  .mat-mdc-button {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
