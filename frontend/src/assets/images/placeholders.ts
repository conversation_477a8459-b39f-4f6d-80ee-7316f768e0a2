// Beautiful placeholder images for the exam system
export const PLACEHOLDER_IMAGES = {
  // Hero section images
  hero: {
    dashboard: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center',
    students: 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=800&h=600&fit=crop&crop=center',
    education: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=600&fit=crop&crop=center',
    technology: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=800&h=600&fit=crop&crop=center'
  },

  // Educational content images
  subjects: {
    mathematics: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400&h=300&fit=crop&crop=center',
    science: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=400&h=300&fit=crop&crop=center',
    history: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop&crop=center',
    literature: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop&crop=center',
    geography: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=300&fit=crop&crop=center',
    physics: 'https://images.unsplash.com/photo-1636466497217-26a8cbeaf0aa?w=400&h=300&fit=crop&crop=center',
    chemistry: 'https://images.unsplash.com/photo-1554475901-4538ddfbccc2?w=400&h=300&fit=crop&crop=center',
    biology: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&crop=center'
  },

  // Feature illustrations
  features: {
    security: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop&crop=center',
    analytics: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center',
    collaboration: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop&crop=center',
    mobile: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop&crop=center',
    cloud: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=300&fit=crop&crop=center',
    ai: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop&crop=center'
  },

  // User avatars
  avatars: {
    student1: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    student2: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    student3: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    teacher1: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    teacher2: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    admin: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face'
  },

  // Background patterns
  patterns: {
    geometric: 'https://images.unsplash.com/photo-1557683316-973673baf926?w=1200&h=800&fit=crop&crop=center',
    abstract: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1200&h=800&fit=crop&crop=center',
    gradient: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop&crop=center'
  },

  // Icons and illustrations
  illustrations: {
    exam: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=500&h=400&fit=crop&crop=center',
    certificate: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=500&h=400&fit=crop&crop=center',
    graduation: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=500&h=400&fit=crop&crop=center',
    books: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500&h=400&fit=crop&crop=center',
    laptop: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=400&fit=crop&crop=center',
    success: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=500&h=400&fit=crop&crop=center'
  },

  // Empty states
  empty: {
    noExams: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=300&h=200&fit=crop&crop=center',
    noResults: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=300&h=200&fit=crop&crop=center',
    noStudents: 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=300&h=200&fit=crop&crop=center',
    noQuestions: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=300&h=200&fit=crop&crop=center'
  }
};

// SVG Icons as data URLs for better performance
export const SVG_ICONS = {
  // Educational icons
  book: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTlWNkMyIDUuNDQ3NzIgMiA0Ljg5NDQzIDIgNC4zNDMxNUMyIDMuNzkxODYgMi40NDc3MiAzLjM0MzE1IDMgMy4zNDMxNUg5QzEwLjEwNDYgMy4zNDMxNSAxMSA0LjIzODU4IDExIDUuMzQzMTVWMTlMMTIgMThMMTMgMTlWNS4zNDMxNUMxMyA0LjIzODU4IDEzLjg5NTQgMy4zNDMxNSAxNSAzLjM0MzE1SDIxQzIxLjU1MjMgMy4zNDMxNSAyMiAzLjc5MDg2IDIyIDQuMzQzMTVDMjIgNC44OTU0MyAyMiA1LjQ0ODcyIDIyIDZWMTlMMTIgMTNMNCAyMFoiIGZpbGw9IiM2MzY2RjEiLz4KPC9zdmc+',
  
  exam: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTJMMTEgMTRMMTUgMTBNMjEgMTJDMjEgMTYuOTcwNiAxNi45NzA2IDIxIDEyIDIxUzcgMTYuOTcwNiA3IDEyUzExLjAyOTQgMyAxNiAzUzIxIDcuMDI5NCAyMSAxMloiIHN0cm9rZT0iIzIyQzU1RSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+',
  
  analytics: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgM1YyMUgyMU0xOSAxMUwxNSA3TDEwIDEyTDYgOCIgc3Ryb2tlPSIjNkY0NkU1IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=',
  
  security: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDIyUzIwIDEzIDIwIDhWM0wxMiAxTDQgM1Y4UzQgMTMgMTIgMjJaIiBzdHJva2U9IiNGNTlFMEIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==',
  
  graduation: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyIDEwVjE2QzIyIDE3LjEwNDYgMjEuMTA0NiAxOCAyMCAxOEg0QzIuODk1NDMgMTggMiAxNy4xMDQ2IDIgMTZWMTBMMTIgNUwyMiAxMFoiIGZpbGw9IiNFRjQ0NDQiLz4KPHBhdGggZD0iTTYgMTBWMTZIMTgiIHN0cm9rZT0iI0VGNDQzNCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+'
};

// Utility function to get random placeholder image
export function getRandomPlaceholder(category: keyof typeof PLACEHOLDER_IMAGES): string {
  const images = Object.values(PLACEHOLDER_IMAGES[category]);
  return images[Math.floor(Math.random() * images.length)];
}

// Utility function to create gradient backgrounds
export function createGradientBackground(colors: string[]): string {
  return `linear-gradient(135deg, ${colors.join(', ')})`;
}

// Common gradient combinations
export const GRADIENT_BACKGROUNDS = {
  primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  success: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
  warning: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
  error: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
  info: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
  purple: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
  pink: 'linear-gradient(135deg, #ec4899 0%, #db2777 100%)',
  ocean: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  sunset: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  forest: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
};
