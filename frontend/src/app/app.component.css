/* Global Responsive Styles */
:host {
  display: block;
  min-height: 100vh;
  width: 100%;
}

/* Ensure proper box-sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Global responsive container */
.app-container {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Responsive breakpoints */
@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0 1rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.25rem;
  }
}

/* Responsive text scaling */
@media (max-width: 768px) {
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.875rem; }
}

@media (max-width: 480px) {
  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.25rem; }
  h3 { font-size: 1.125rem; }
  h4 { font-size: 1rem; }
  h5 { font-size: 0.875rem; }
  h6 { font-size: 0.75rem; }
}