import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-hero-section',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    RouterModule
  ],
  template: `
    <section class="hero-section">
      <!-- Animated Background -->
      <div class="hero-background">
        <div class="floating-shapes">
          <div class="shape shape-1 animate-float"></div>
          <div class="shape shape-2 animate-float-reverse"></div>
          <div class="shape shape-3 animate-float"></div>
          <div class="shape shape-4 animate-float-reverse"></div>
          <div class="shape shape-5 animate-float"></div>
        </div>
        <div class="gradient-overlay"></div>
      </div>

      <!-- Hero Content -->
      <div class="hero-container">
        <div class="hero-content">
          <!-- Main Content -->
          <div class="hero-main animate-fade-in-up">
            <div class="hero-badge animate-scale-in stagger-1">
              <mat-icon>verified</mat-icon>
              <span>{{ badge || 'Trusted by 10,000+ Students' }}</span>
            </div>
            
            <h1 class="hero-title animate-fade-in-up stagger-2">
              {{ title || 'Transform Your Learning Journey' }}
            </h1>
            
            <p class="hero-subtitle animate-fade-in-up stagger-3">
              {{ subtitle || 'Experience the future of online examinations with our cutting-edge platform designed for academic excellence and seamless learning.' }}
            </p>

            <!-- Feature Highlights -->
            <div class="feature-highlights animate-fade-in-up stagger-4">
              <div class="feature-item" *ngFor="let feature of features; let i = index" 
                   [style.animation-delay]="(0.5 + i * 0.1) + 's'">
                <div class="feature-icon">
                  <mat-icon>{{ feature.icon }}</mat-icon>
                </div>
                <span>{{ feature.text }}</span>
              </div>
            </div>

            <!-- Call to Action -->
            <div class="hero-actions animate-fade-in-up stagger-5">
              <button mat-raised-button class="cta-primary" [routerLink]="primaryAction.link">
                <mat-icon>{{ primaryAction.icon }}</mat-icon>
                <span>{{ primaryAction.text }}</span>
              </button>
              
              <button mat-stroked-button class="cta-secondary" [routerLink]="secondaryAction.link">
                <mat-icon>{{ secondaryAction.icon }}</mat-icon>
                <span>{{ secondaryAction.text }}</span>
              </button>
            </div>

            <!-- Stats -->
            <div class="hero-stats animate-fade-in-up stagger-6" *ngIf="stats.length > 0">
              <div class="stat-item" *ngFor="let stat of stats; let i = index"
                   [style.animation-delay]="(0.7 + i * 0.1) + 's'">
                <div class="stat-number">{{ stat.number }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>

          <!-- Hero Image/Visual -->
          <div class="hero-visual animate-fade-in-right stagger-3">
            <div class="visual-container">
              <div class="visual-card glass-card animate-float">
                <div class="card-header">
                  <div class="card-dots">
                    <span class="dot dot-red"></span>
                    <span class="dot dot-yellow"></span>
                    <span class="dot dot-green"></span>
                  </div>
                  <div class="card-title">ExamMaster Dashboard</div>
                </div>
                <div class="card-content">
                  <div class="mock-chart">
                    <div class="chart-bar" style="height: 60%"></div>
                    <div class="chart-bar" style="height: 80%"></div>
                    <div class="chart-bar" style="height: 45%"></div>
                    <div class="chart-bar" style="height: 90%"></div>
                    <div class="chart-bar" style="height: 70%"></div>
                  </div>
                  <div class="mock-progress">
                    <div class="progress-item">
                      <span>Mathematics</span>
                      <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                      </div>
                    </div>
                    <div class="progress-item">
                      <span>Science</span>
                      <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%"></div>
                      </div>
                    </div>
                    <div class="progress-item">
                      <span>History</span>
                      <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Floating Elements -->
              <div class="floating-element element-1 animate-float">
                <mat-icon>school</mat-icon>
              </div>
              <div class="floating-element element-2 animate-float-reverse">
                <mat-icon>analytics</mat-icon>
              </div>
              <div class="floating-element element-3 animate-float">
                <mat-icon>verified</mat-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="scroll-indicator animate-bounce-in">
        <div class="scroll-arrow">
          <mat-icon>keyboard_arrow_down</mat-icon>
        </div>
        <span>Scroll to explore</span>
      </div>
    </section>
  `,
  styleUrls: ['./hero-section.component.css']
})
export class HeroSectionComponent {
  @Input() title?: string;
  @Input() subtitle?: string;
  @Input() badge?: string;
  
  @Input() features = [
    { icon: 'security', text: 'Secure Testing' },
    { icon: 'analytics', text: 'Real-time Analytics' },
    { icon: 'devices', text: 'Multi-device Support' },
    { icon: 'speed', text: 'Lightning Fast' }
  ];

  @Input() primaryAction = {
    text: 'Get Started',
    icon: 'rocket_launch',
    link: '/register'
  };

  @Input() secondaryAction = {
    text: 'Learn More',
    icon: 'play_circle',
    link: '/about'
  };

  @Input() stats = [
    { number: '10K+', label: 'Students' },
    { number: '500+', label: 'Exams' },
    { number: '98%', label: 'Success Rate' },
    { number: '24/7', label: 'Support' }
  ];
}
