/* Modern Image Gallery with Lightbox */
.image-gallery {
  width: 100%;
  padding: 1rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.gallery-item {
  position: relative;
  cursor: pointer;
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gallery-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.image-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.gallery-item:hover .gallery-image {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.8) 0%,
    rgba(139, 69, 19, 0.8) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.gallery-item:hover .image-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: white;
  padding: 1rem;
}

.zoom-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  margin-bottom: 0.5rem;
  animation: pulse 2s infinite;
}

.image-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.image-description {
  font-size: 0.875rem;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Lightbox Styles */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 100%;
  max-height: 100%;
}

.lightbox-image {
  max-width: 80vw;
  max-height: 70vh;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-2xl);
}

.lightbox-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  padding: 1rem 2rem;
  margin-top: 1rem;
  text-align: center;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.lightbox-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.lightbox-info p {
  margin: 0;
  opacity: 0.9;
}

/* Navigation Buttons */
.close-btn {
  position: absolute;
  top: -60px;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.1);
}

.prev-btn {
  left: -80px;
}

.next-btn {
  right: -80px;
}

.nav-btn mat-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .image-container {
    height: 200px;
  }

  .lightbox-image {
    max-width: 95vw;
    max-height: 60vh;
  }

  .close-btn {
    top: -50px;
    right: 10px;
    width: 40px;
    height: 40px;
  }

  .nav-btn {
    width: 48px;
    height: 48px;
  }

  .prev-btn {
    left: -60px;
  }

  .next-btn {
    right: -60px;
  }

  .lightbox-info {
    padding: 0.75rem 1rem;
    margin-top: 0.5rem;
  }

  .lightbox-info h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .prev-btn {
    left: 10px;
  }

  .next-btn {
    right: 10px;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
  }

  .nav-btn mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}
