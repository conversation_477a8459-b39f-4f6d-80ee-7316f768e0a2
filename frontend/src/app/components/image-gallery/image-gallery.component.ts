import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

export interface GalleryImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
}

@Component({
  selector: 'app-image-gallery',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule
  ],
  template: `
    <div class="image-gallery">
      <div class="gallery-grid">
        <div 
          *ngFor="let image of images; let i = index" 
          class="gallery-item animate-fade-in-up"
          [style.animation-delay]="(i * 0.1) + 's'"
          (click)="openLightbox(i)">
          <div class="image-container">
            <img 
              [src]="image.src" 
              [alt]="image.alt"
              class="gallery-image"
              loading="lazy">
            <div class="image-overlay">
              <div class="overlay-content">
                <mat-icon class="zoom-icon">zoom_in</mat-icon>
                <h3 *ngIf="image.title" class="image-title">{{ image.title }}</h3>
                <p *ngIf="image.description" class="image-description">{{ image.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Lightbox -->
      <div *ngIf="lightboxOpen" class="lightbox-overlay" (click)="closeLightbox()">
        <div class="lightbox-container animate-scale-in">
          <button mat-icon-button class="close-btn" (click)="closeLightbox()">
            <mat-icon>close</mat-icon>
          </button>
          
          <button mat-icon-button class="nav-btn prev-btn" (click)="previousImage(); $event.stopPropagation()">
            <mat-icon>chevron_left</mat-icon>
          </button>
          
          <div class="lightbox-content">
            <img 
              [src]="images[currentImageIndex].src" 
              [alt]="images[currentImageIndex].alt"
              class="lightbox-image">
            <div class="lightbox-info" *ngIf="images[currentImageIndex].title || images[currentImageIndex].description">
              <h3 *ngIf="images[currentImageIndex].title">{{ images[currentImageIndex].title }}</h3>
              <p *ngIf="images[currentImageIndex].description">{{ images[currentImageIndex].description }}</p>
            </div>
          </div>
          
          <button mat-icon-button class="nav-btn next-btn" (click)="nextImage(); $event.stopPropagation()">
            <mat-icon>chevron_right</mat-icon>
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./image-gallery.component.css']
})
export class ImageGalleryComponent {
  @Input() images: GalleryImage[] = [];
  
  lightboxOpen = false;
  currentImageIndex = 0;

  openLightbox(index: number): void {
    this.currentImageIndex = index;
    this.lightboxOpen = true;
    document.body.style.overflow = 'hidden';
  }

  closeLightbox(): void {
    this.lightboxOpen = false;
    document.body.style.overflow = 'auto';
  }

  nextImage(): void {
    this.currentImageIndex = (this.currentImageIndex + 1) % this.images.length;
  }

  previousImage(): void {
    this.currentImageIndex = this.currentImageIndex === 0 
      ? this.images.length - 1 
      : this.currentImageIndex - 1;
  }
}
