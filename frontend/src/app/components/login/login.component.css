/* Login Background */
.login-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Background Animation */
.bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  bottom: 10%;
  right: 10%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Login Center Container */
.login-center {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  position: relative;
  z-index: 1;
  padding: var(--spacing-md);
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 450px;
  width: 100%;
}

/* Brand Section */
.brand-section {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  color: white;
}

.brand-icon {
  margin-bottom: var(--spacing-md);
}

.brand-logo {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  color: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 var(--spacing-sm) 0;
  background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

/* Login Card */
.login-card {
  width: 100%;
  max-width: 420px;
  padding: var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  transition: all var(--transition-normal);
}

.login-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 12px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* Login Header */
.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.login-title {
  font-size: 1.75rem !important;
  font-weight: 700 !important;
  color: var(--primary-700) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--spacing-sm) !important;
  margin: 0 !important;
}

.title-icon {
  font-size: 1.75rem !important;
  width: 1.75rem !important;
  height: 1.75rem !important;
}

/* Login Content */
.login-content {
  padding: 0 !important;
}

/* Form Fields */
.full-width {
  width: 100%;
}

.mat-mdc-form-field {
  margin-bottom: var(--spacing-lg);
}

.mat-mdc-form-field .mat-mdc-text-field-wrapper {
  background: rgba(248, 250, 252, 0.8);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.mat-mdc-form-field:hover .mat-mdc-text-field-wrapper {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
}

.mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {
  background: rgba(248, 250, 252, 1);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Login Options */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--spacing-lg) 0;
}

.remember-me {
  font-size: 0.9rem;
}

.forgot-password {
  color: var(--primary-600);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.forgot-password:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* Login Button */
.login-btn {
  width: 100%;
  height: 56px;
  margin: var(--spacing-lg) 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  background: var(--gradient-primary);
  color: white;
  border: none;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.login-btn:active {
  transform: translateY(-1px);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-lg);
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: var(--spacing-xl) 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--neutral-300), transparent);
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 var(--spacing-md);
  color: var(--neutral-600);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Register Section */
.register-section {
  text-align: center;
  margin-top: var(--spacing-lg);
}

.register-text {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--neutral-600);
  font-size: 0.95rem;
}

.register-btn {
  color: var(--primary-600);
  font-weight: 600;
  border: 2px solid var(--primary-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.register-btn:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-1px);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  margin-top: var(--spacing-lg);
  font-size: 1.1rem;
  font-weight: 500;
}

/* Snackbar Styles */
.snackbar-success {
  background: var(--success-600) !important;
  color: white !important;
}

.snackbar-warn {
  background: var(--error-600) !important;
  color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-bg {
    padding: var(--spacing-md);
  }

  .brand-title {
    font-size: 2rem;
  }

  .brand-logo {
    font-size: 3rem !important;
    width: 3rem !important;
    height: 3rem !important;
  }

  .login-card {
    padding: var(--spacing-xl);
    margin: var(--spacing-md);
  }

  .shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .brand-section {
    margin-bottom: var(--spacing-xl);
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .login-card {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
  }

  .login-title {
    font-size: 1.5rem !important;
  }

  .login-options {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mat-mdc-form-field .mat-mdc-text-field-wrapper {
    background: rgba(51, 65, 85, 0.8);
  }

  .mat-mdc-form-field:hover .mat-mdc-text-field-wrapper {
    background: rgba(51, 65, 85, 1);
  }

  .divider-text {
    background: rgba(30, 41, 59, 0.95);
    color: var(--neutral-400);
  }

  .register-text {
    color: var(--neutral-400);
  }
}

/* Accessibility Improvements */
.login-btn:focus-visible,
.register-btn:focus-visible,
.forgot-password:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .login-card {
    border: 3px solid var(--primary-700);
  }

  .login-btn {
    border: 2px solid var(--primary-800);
  }

  .register-btn {
    border: 2px solid var(--primary-700);
  }
}

/* Animation Performance */
@media (prefers-reduced-motion: reduce) {
  .shape {
    animation: none;
  }

  .login-btn::before {
    display: none;
  }

  * {
    transition-duration: 0.01ms !important;
  }
}