<div class="question-manage-root">
  <mat-card>
    <mat-card-title>Manage Questions</mat-card-title>
    <mat-card-content>
      <form *ngIf="!editingQuestion" (ngSubmit)="createQuestion()" class="question-form">
        <mat-form-field appearance="outline">
          <mat-label>Exam ID</mat-label>
          <input matInput [(ngModel)]="newQuestion.examId" name="examId" required (blur)="loadQuestions(newQuestion.examId!)">
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Question Text</mat-label>
          <input matInput [(ngModel)]="newQuestion.text" name="text" required>
        </mat-form-field>
        <div class="options-row">
          <mat-form-field appearance="outline" *ngFor="let opt of [0,1,2,3]">
            <mat-label>Option {{ opt + 1 }}</mat-label>
            <input matInput [(ngModel)]="newQuestion.options![opt]" [name]="'option' + opt" required>
          </mat-form-field>
        </div>
        <mat-form-field appearance="outline">
          <mat-label>Correct Answer (0-3)</mat-label>
          <input matInput type="number" [(ngModel)]="newQuestion.correctAnswer" name="correctAnswer" required min="0" max="3">
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Marks</mat-label>
          <input matInput type="number" [(ngModel)]="newQuestion.marks" name="marks" required min="1">
        </mat-form-field>
        <button mat-raised-button color="primary" type="submit">Add Question</button>
      </form>
      <form *ngIf="editingQuestion" (ngSubmit)="updateQuestion()" class="question-form">
        <mat-form-field appearance="outline">
          <mat-label>Question Text</mat-label>
          <input matInput [(ngModel)]="editingQuestion.text" name="editText" required>
        </mat-form-field>
        <div class="options-row">
          <mat-form-field appearance="outline" *ngFor="let opt of [0,1,2,3]">
            <mat-label>Option {{ opt + 1 }}</mat-label>
            <input matInput [(ngModel)]="editingQuestion.options![opt]" [name]="'editOption' + opt" required>
          </mat-form-field>
        </div>
        <mat-form-field appearance="outline">
          <mat-label>Correct Answer (0-3)</mat-label>
          <input matInput type="number" [(ngModel)]="editingQuestion.correctAnswer" name="editCorrectAnswer" required min="0" max="3">
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Marks</mat-label>
          <input matInput type="number" [(ngModel)]="editingQuestion.marks" name="editMarks" required min="1">
        </mat-form-field>
        <button mat-raised-button color="accent" type="submit">Update</button>
        <button mat-button type="button" (click)="cancelEdit()">Cancel</button>
      </form>
    </mat-card-content>
  </mat-card>

  <mat-card class="question-list-card">
    <mat-card-title>All Questions</mat-card-title>
    <mat-card-content>
      <table mat-table [dataSource]="questions" class="mat-elevation-z1">
        <ng-container matColumnDef="text">
          <th mat-header-cell *matHeaderCellDef>Text</th>
          <td mat-cell *matCellDef="let q">{{ q.text }}</td>
        </ng-container>
        <ng-container matColumnDef="options">
          <th mat-header-cell *matHeaderCellDef>Options</th>
          <td mat-cell *matCellDef="let q">{{ q.options.join(', ') }}</td>
        </ng-container>
        <ng-container matColumnDef="correctAnswer">
          <th mat-header-cell *matHeaderCellDef>Correct</th>
          <td mat-cell *matCellDef="let q">{{ q.correctAnswer + 1 }}</td>
        </ng-container>
        <ng-container matColumnDef="marks">
          <th mat-header-cell *matHeaderCellDef>Marks</th>
          <td mat-cell *matCellDef="let q">{{ q.marks }}</td>
        </ng-container>
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let q">
            <button mat-icon-button color="primary" (click)="startEdit(q)"><mat-icon>edit</mat-icon></button>
            <button mat-icon-button color="warn" (click)="deleteQuestion(q._id)"><mat-icon>delete</mat-icon></button>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="['text', 'options', 'correctAnswer', 'marks', 'actions']"></tr>
        <tr mat-row *matRowDef="let row; columns: ['text', 'options', 'correctAnswer', 'marks', 'actions']"></tr>
      </table>
      <div *ngIf="loading" class="loading-spinner">
        <mat-progress-spinner diameter="32" color="primary" mode="indeterminate"></mat-progress-spinner>
      </div>
    </mat-card-content>
  </mat-card>
</div>
